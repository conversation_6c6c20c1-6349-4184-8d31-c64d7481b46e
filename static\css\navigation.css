/* Navigation Styles */

/* Main Header */
.main-header {
  background-color: var(--white);
  box-shadow: var(--shadow-base);
  position: sticky;
  top: 0;
  z-index: var(--z-50);
  padding: var(--spacing-base) 0;
  width: 100%;
  border-bottom: 1px solid var(--gray-100);
}

.main-header .container {
  width: 100%;
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-base);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-base);
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.logo a {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-xl);
  font-weight: var(--fw-bold);
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition-base);
}

.logo a:hover {
  color: var(--primary-dark);
  text-decoration: none;
}

.logo img {
  height: 44px;
  width: auto;
}

.logo span {
  font-family: var(--font-family-sans);
  letter-spacing: -0.02em;
}

/* Search Bar */
.search-bar {
  flex: 1;
  max-width: 450px;
  position: relative;
  margin: 0 var(--spacing-lg);
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-lg);
  padding-right: 45px;
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-full);
  font-size: var(--font-sm);
  font-family: var(--font-family-sans);
  background-color: var(--gray-50);
  transition: var(--transition-base);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary);
  background-color: var(--white);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.search-input::placeholder {
  color: var(--gray-500);
  font-weight: var(--fw-regular);
}

.search-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--primary);
  border: none;
  color: var(--white);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-base);
  font-size: var(--font-sm);
}

.search-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-50%) scale(1.05);
}

/* Main Navigation */
.main-nav {
  display: flex;
  align-items: center;
}

.main-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--spacing-sm);
}

.main-nav li {
  position: relative;
}

.main-nav a {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text);
  font-weight: var(--fw-medium);
  font-size: var(--font-sm);
  padding: var(--spacing-sm) var(--spacing-base);
  border-radius: var(--radius-lg);
  transition: var(--transition-base);
  text-decoration: none;
  white-space: nowrap;
}

.main-nav a:hover {
  color: var(--primary);
  background-color: var(--primary-light);
  text-decoration: none;
  transform: translateY(-1px);
}

.main-nav a.active {
  color: var(--primary);
  background-color: var(--primary-light);
  font-weight: var(--fw-semibold);
}

.main-nav a i {
  font-size: var(--font-xs);
  width: 16px;
  text-align: center;
}

/* User Actions */
.user-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}

.cart-icon, .notification-icon, .message-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42px;
  height: 42px;
  border-radius: var(--radius-lg);
  color: var(--text);
  background-color: var(--gray-50);
  border: 1px solid var(--gray-200);
  transition: var(--transition-base);
  text-decoration: none;
}

.cart-icon:hover, .notification-icon:hover, .message-icon:hover {
  background-color: var(--primary);
  color: var(--white);
  border-color: var(--primary);
  transform: translateY(-1px);
  text-decoration: none;
}

.cart-count, .notification-count, .message-count {
  position: absolute;
  top: -6px;
  right: -6px;
  background-color: var(--primary);
  color: var(--white);
  font-size: 11px;
  font-weight: var(--fw-bold);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--white);
  box-shadow: var(--shadow-sm);
}

/* User Dropdown */
.user-dropdown {
  position: relative;
  margin-left: var(--spacing-sm);
}

.dropdown-toggle {
  background: none;
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  cursor: pointer;
  padding: 2px;
  display: flex;
  align-items: center;
  transition: var(--transition-base);
  overflow: hidden;
}

.dropdown-toggle:hover {
  border-color: var(--primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.avatar {
  width: 38px;
  height: 38px;
  border-radius: var(--radius-md);
  object-fit: cover;
  display: block;
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  min-width: 220px;
  z-index: var(--z-50);
  padding: var(--spacing-base);
  display: none;
  border: 1px solid var(--gray-200);
}

.dropdown-menu a {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-base);
  color: var(--text);
  font-size: var(--font-sm);
  font-weight: var(--fw-medium);
  border-radius: var(--radius-md);
  transition: var(--transition-base);
  text-decoration: none;
  margin-bottom: 2px;
}

.dropdown-menu a:hover {
  background-color: var(--primary-light);
  color: var(--primary);
  text-decoration: none;
  transform: translateX(2px);
}

.dropdown-menu a i {
  width: 18px;
  text-align: center;
  color: var(--gray-500);
}

.dropdown-menu a:hover i {
  color: var(--primary);
}

.user-dropdown:hover .dropdown-menu {
  display: block;
  animation: fadeInDown 0.2s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--text);
  font-size: var(--font-lg);
  cursor: pointer;
}

/* Mobile Menu */
.mobile-menu {
  position: fixed;
  top: 0;
  right: -300px;
  width: 300px;
  height: 100vh;
  background-color: var(--white);
  z-index: var(--z-50);
  box-shadow: var(--shadow-lg);
  transition: right 0.3s ease-in-out;
  overflow-y: auto;
}

.mobile-menu.active {
  right: 0;
}

.mobile-menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-base);
  border-bottom: 1px solid var(--gray-200);
}

.mobile-menu-close {
  background: none;
  border: none;
  color: var(--text);
  font-size: var(--font-lg);
  cursor: pointer;
}

.mobile-search {
  padding: var(--spacing-base);
  border-bottom: 1px solid var(--gray-200);
}

.mobile-nav ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mobile-nav a {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-base);
  color: var(--text);
  border-bottom: 1px solid var(--gray-200);
  transition: var(--transition-base);
}

.mobile-nav a:hover, .mobile-nav a.active {
  background-color: var(--gray-100);
  color: var(--primary);
}

.mobile-nav i {
  width: 20px;
  text-align: center;
}

/* Responsive Styles */
@media (min-width: 769px) {
  .main-nav {
    display: flex !important;
  }

  .mobile-menu-toggle {
    display: none !important;
  }

  .mobile-menu {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    z-index: -1 !important;
    transform: translateX(100%) !important;
  }

  /* Ensure body overflow is not affected on desktop */
  body {
    overflow: auto !important;
  }
}

@media (max-width: 1024px) {
  .search-bar {
    max-width: 350px;
    margin: 0 var(--spacing-base);
  }

  .main-nav ul {
    gap: var(--spacing-xs);
  }

  .main-nav a {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-xs);
  }
}

@media (max-width: 768px) {
  .main-nav {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .search-bar {
    order: 3;
    max-width: 100%;
    width: 100%;
    margin: var(--spacing-sm) 0 0 0;
  }

  .header-content {
    flex-wrap: wrap;
  }

  .main-header {
    padding: var(--spacing-sm) 0;
  }

  .user-actions .cart-icon,
  .user-actions .notification-icon,
  .user-actions .message-icon {
    display: none;
  }

  .main-header .container {
    padding: 0 var(--spacing-sm);
  }

  .logo img {
    height: 38px;
  }

  .logo a {
    font-size: var(--font-lg);
  }
}

@media (max-width: 480px) {
  .search-bar {
    margin: var(--spacing-xs) 0 0 0;
  }

  .header-content {
    gap: var(--spacing-sm);
  }

  .user-actions {
    gap: var(--spacing-xs);
  }
}

/* Auth Buttons */
.user-actions .btn {
  font-size: var(--font-sm);
  font-weight: var(--fw-medium);
  padding: var(--spacing-sm) var(--spacing-base);
  border-radius: var(--radius-lg);
  text-decoration: none;
  transition: var(--transition-base);
  border: 2px solid transparent;
  white-space: nowrap;
}

.user-actions .btn-primary {
  background-color: var(--primary);
  color: var(--white);
  border-color: var(--primary);
}

.user-actions .btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-1px);
  text-decoration: none;
}

.user-actions .btn-secondary {
  background-color: transparent;
  color: var(--primary);
  border-color: var(--primary);
}

.user-actions .btn-secondary:hover {
  background-color: var(--primary);
  color: var(--white);
  transform: translateY(-1px);
  text-decoration: none;
}

/* Mobile Menu Enhancements */
.mobile-menu-toggle {
  background: var(--gray-50);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  color: var(--text);
  font-size: var(--font-lg);
  cursor: pointer;
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-base);
}

.mobile-menu-toggle:hover {
  background-color: var(--primary);
  color: var(--white);
  border-color: var(--primary);
  transform: translateY(-1px);
}

/* Enhanced Mobile Menu - Only for mobile devices */
@media (max-width: 768px) {
  .mobile-menu {
    background-color: var(--white);
    border-radius: var(--radius-lg) 0 0 var(--radius-lg);
    box-shadow: var(--shadow-2xl);
    border-left: 4px solid var(--primary);
  }

  .mobile-menu-header {
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-light) 100%);
    border-radius: var(--radius-lg) 0 0 0;
    margin: -1px;
    margin-bottom: 0;
  }

  .mobile-menu-close {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    color: var(--text);
    font-size: var(--font-base);
    cursor: pointer;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-base);
  }

  .mobile-menu-close:hover {
    background-color: var(--danger);
    color: var(--white);
    border-color: var(--danger);
  }

  .mobile-search {
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
  }

  .mobile-nav a {
    font-weight: var(--fw-medium);
    border-radius: var(--radius-md);
    margin: 2px var(--spacing-sm);
    transition: var(--transition-base);
  }

  .mobile-nav a:hover, .mobile-nav a.active {
    background-color: var(--primary-light);
    color: var(--primary);
    transform: translateX(4px);
    border-color: transparent;
  }
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Header backdrop blur effect */
.main-header {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Loading states */
.search-button.loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: translateY(-50%) rotate(0deg); }
  to { transform: translateY(-50%) rotate(360deg); }
}

/* Focus states for accessibility */
.main-nav a:focus,
.cart-icon:focus,
.notification-icon:focus,
.message-icon:focus,
.dropdown-toggle:focus,
.mobile-menu-toggle:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .main-header {
    border-bottom-width: 2px;
  }

  .search-input {
    border-width: 2px;
  }

  .main-nav a,
  .cart-icon,
  .notification-icon,
  .message-icon {
    border: 1px solid var(--gray-400);
  }
}